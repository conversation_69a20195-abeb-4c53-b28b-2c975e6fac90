/**
 * @type {import('next').NextConfig}
 **/
module.exports = {
  webpack: function (config, { defaultLoaders, isServer }) {
    if (!isServer) config.resolve.fallback.fs = false;

    const resolvedBaseUrl = require('path').resolve(config.context, '../../');
    config.plugins = config.plugins || [];

    config.module.rules = [
      ...config.module.rules,
      {
        test: /\.(tsx|ts|js|mjs|jsx)$/,
        include: [resolvedBaseUrl],
        use: defaultLoaders.babel,
        exclude: (excludePath) => {
          return /node_modules/.test(excludePath);
        },
      },
    ];

    return config;
  },

  eslint: {
    ignoreDuringBuilds: true,
  },

  typescript: {
    tsconfigPath: makeConfigPath(),
    ignoreBuildErrors: false,
    // ignoreBuildErrors: process.env.NODE_ENV === "development" ? false : true,
  },
  env: {
    NEXT_PUBLIC_DEV_API_URL: 'https://localhost:5001/',
  },
  compiler: {
    styledComponents: true,
  },
  poweredByHeader: false,
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'X-Xss-Protection', value: '1; mode=block' },
        ],
      },
    ];
  },
};

function makeConfigPath() {
  return process.env.NODE_ENV === 'development' ? '../../tsconfig.json' : '../tsconfig.json';
}
