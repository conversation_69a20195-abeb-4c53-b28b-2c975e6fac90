# Install dependencies only when needed
#docker build -f public/Dockerfile -t orange-demo-public .
#docker run -p 3000:3000 -t orange-demo-public

FROM mirror.gcr.io/node:alpine AS builder

RUN npm install -g corepack
RUN corepack enable 
RUN corepack prepare yarn@4.3.1 --activate
RUN ls && yarn -v
RUN mkdir -p /tmp
COPY . ./tmp
WORKDIR /tmp

RUN yarn install --immutable

RUN export NODE_OPTIONS=--openssl-legacy-provider && yarn public-build

FROM mirror.gcr.io/node:alpine AS runner

RUN corepack enable
RUN corepack prepare yarn@4.3.1 --activate
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0

WORKDIR /app

#ENV FileStorage home/nextjs/dev
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_TLS_REJECT_UNAUTHORIZED 0

# ENV apiBaseUrlInternal https://192.168.0.102:5001
# ENV apiBaseUrl https://192.168.0.102:5001
# ENV apiBaseUrlInternal https://apifront.test.koenigfinance.com
# ENV apiBaseUrl https://apifront.test.koenigfinance.com

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /tmp .

COPY --from=builder /tmp/public/dist ./dist
COPY --from=builder /tmp/public/src/public ./public
COPY --from=builder --chown=nextjs:nodejs /tmp/public/.next ./.next

USER nextjs

EXPOSE 3000

CMD ["yarn", "public-prod"]
