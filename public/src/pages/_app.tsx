import React from 'react';
import { ThemeProvider } from 'styled-components';
import { theme } from 'public/src/theme';
import { publicReduxWrapper } from '@public/redux';
import { ModalOverlay } from '@common/components/modal-overlay';
import { SessionTimer } from '@common/components/session-timer';
import { TranslatesUpdate } from '@common/components/translates-update';
import { AppProps } from 'next/app';
import { ProgressLoader } from '@common/components/loaders/progress-loader';
import '@fortawesome/fontawesome-free/css/all.css';
import { PageTitle } from '@common/components/page-title';
import { GlobalStyles } from '../theme/global-styles';
import { ToastProvider } from 'react-toast-notifications';

export default publicReduxWrapper.withRedux(function ({ Component, pageProps }: AppProps) {
  return (
    <React.Fragment>
      <TranslatesUpdate />
      <ToastProvider newestOnTop autoDismiss={true}>
        {
          //@ts-ignore
          <ThemeProvider theme={theme}>
            {
              //@ts-ignore
              <GlobalStyles />
            }
            <ProgressLoader />
            <React.Fragment>
              <PageTitle {...(pageProps as any)}></PageTitle>
              <Component {...pageProps} />
            </React.Fragment>
            <ModalOverlay />
            <SessionTimer />
          </ThemeProvider>
        }
      </ToastProvider>
    </React.Fragment>
  );
});
