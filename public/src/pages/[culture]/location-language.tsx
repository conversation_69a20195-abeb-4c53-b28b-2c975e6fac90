import { publicReduxWrapper } from '@public/redux';
import { pageService } from '@common/services/page-service';

import LocationLanguagePage from '@public/page/locales-page/location-language';

export default function () {
  return <LocationLanguagePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(publicReduxWrapper, {
  propsLoader: LocationLanguagePage.serverProps,
});
