import React from 'react';
import { pageService } from '@common/services/page-service';
import { publicReduxWrapper } from '@public/redux';
import { ContentNewsModel } from '@common/http/services/public-http-service';
import CompanyPressReleasesNewPage from '@public/page/locales-page/company/press-releases/[filename]';

export default function (props: { data: ContentNewsModel }) {
  return <CompanyPressReleasesNewPage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(publicReduxWrapper, {
  propsLoader: CompanyPressReleasesNewPage.serverProps,
});
