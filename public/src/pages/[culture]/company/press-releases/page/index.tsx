import { pageService } from '@common/services/page-service';
import { publicReduxWrapper } from '@public/redux';

export default function () {
  return null;
}

function getProps() {
  return {
    redirect: {
      permanent: false,
      destination: `/company/press-releases/page/1`,
    },
  };
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(publicReduxWrapper, {
  propsLoader: getProps,
});
