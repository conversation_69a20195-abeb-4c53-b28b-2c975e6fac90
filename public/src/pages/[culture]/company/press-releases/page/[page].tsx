import React from 'react';
import { pageService } from '@common/services/page-service';
import { publicReduxWrapper } from '@public/redux';
import { ContentNewsModelQueryWithTotal } from '@common/http/services/public-http-service';
import CompanyPressReleasesFirstPage from '@public/page/locales-page/company/press-releases/page/[page]';

export default function (props: { data: ContentNewsModelQueryWithTotal; page: number }) {
  return <CompanyPressReleasesFirstPage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(publicReduxWrapper, {
  propsLoader: CompanyPressReleasesFirstPage.serverProps,
});
