import { pageService } from '@common/services/page-service';
import { publicReduxWrapper } from '@public/redux';
import SitemapLocaleXMLPage from '@public/page/locales-page/sitemap-locale.xml';

export default function () {
  return <SitemapLocaleXMLPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(publicReduxWrapper, {
  propsLoader: SitemapLocaleXMLPage.serverProps,
});
