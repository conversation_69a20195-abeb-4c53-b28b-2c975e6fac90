import { configureStore } from '@reduxjs/toolkit';
import { createWrapper } from 'next-redux-wrapper';
import { defaultReducer } from '@common/redux';
import { TypedUseSelectorHook, useSelector, useDispatch } from 'react-redux';
import sitemapSlice from '@dev/redux/sitemap-slice';

function makeStore() {
  let store = configureStore({
    reducer: {
      ...defaultReducer,
      [sitemapSlice.name]: sitemapSlice.reducer,
    },
    devTools: false,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: false }),
  });
  return store;
}

let store = makeStore();

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const devReduxWrapper = createWrapper(makeStore);
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
