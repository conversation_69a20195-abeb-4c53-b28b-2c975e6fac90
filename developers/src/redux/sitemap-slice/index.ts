import { createSlice } from '@reduxjs/toolkit';
import { hydrateReducer } from '@common/redux/utils';
import { SiteMap } from '@common/models/SiteMap';
import { siteMap } from '@dev/dev-sitemap';

const name = 'sitemap';
const initialState: { value: SiteMap[] } = { value: siteMap };

const sitemapSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: {
    ...hydrateReducer(name),
  },
});

export default sitemapSlice;
