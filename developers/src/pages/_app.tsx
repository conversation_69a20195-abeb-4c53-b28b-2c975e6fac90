import React from 'react';
import { ThemeProvider } from 'styled-components';
import { GlobalStyles } from '../theme/global-styles';
import { devTheme } from '../theme/index';
import { devReduxWrapper } from '@dev/redux';

import { TranslatesUpdate } from '@common/components/translates-update';
import { AppProps } from 'next/app';
import '@fortawesome/fontawesome-free/css/all.css';
import '@fancyapps/ui/dist/fancybox.css';

export default devReduxWrapper.withRedux(function ({ Component, pageProps }: AppProps) {
  return (
    <>
      <TranslatesUpdate />
      {
        //@ts-ignore
        <ThemeProvider theme={devTheme}>
          {
            //@ts-ignore
            <GlobalStyles />
          }
          <Component {...pageProps} />
        </ThemeProvider>
      }
    </>
  );
});
