import React from "react";
import Head from 'next/head'
import { pageService } from "@common/services/page-service";
import { devReduxWrapper } from "@dev/redux";
import { useTranslate } from "@common/hooks/useTranslate";
import { Content, Header } from "@dev/features";
import Footer from "@dev/components/footer";

export default function Index() {

  const { t } = useTranslate();

  return (
    <>
      <Head>
        <title>{t('public.dev.title')}</title>
        {process?.env?.TestServer ? <meta name="robots" content="noindex"></meta> : null}
      </Head>
      <Header />
      <Content />
      <Footer />
    </>
  );
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(devReduxWrapper);

