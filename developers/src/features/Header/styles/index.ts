import styled from "styled-components";

export const Inner = styled.header`
  background-color: #0d0d0d;
  justify-content: center;
  padding: 1.2rem 0;
  display: flex;
  width: 100%;
`;

export const Container = styled.div`
  align-items: center;
  max-width: 1110px;
  display: flex;
  width: 100%;

  @media (max-width: 1200px) {
    padding: 0 2rem;
  }

  @media (max-width: 500px) {
    padding: 0 10px;
  }
`;

export const Logo = styled.img`
  max-width: 100%,
`;

export const Info = styled.span`
  background-color:  ${({ theme }) => theme.colors.main_2};
  margin-left: 0.5rem;
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 10px;
  color: white;
`;
