import React from "react";

import { Navigation, Item, Subitem } from "./styles";
import { useTranslate } from "@common/hooks/useTranslate";

export default function Sidebar() {


  const { t } = useTranslate();

  return (
    <Navigation>
      <Item href="#what_api">
        {t("public.dev.menu.what_api")}
      </Item>

      <Item href="#api_overview">
        {t("public.dev.menu.api_overview")}
      </Item>

      <Subitem href="#security">
        {t("public.dev.menu.security")}
      </Subitem>

      <Subitem href="#how_the">
        {t("public.dev.menu.how_the")}
      </Subitem>

      <Item href="#api_setup">
        {t("public.dev.menu.api_setup")}
      </Item>

      <Subitem href="#creationg_an_api">
        {t("public.dev.menu.creationg_an_api")}
      </Subitem>

      <Subitem href="#api_key">
        {t("public.dev.menu.api_key")}
      </Subitem>

      <Item href="#api_function_list">
        {t("public.dev.menu.api_function_list")}
      </Item>

      <Subitem href="#swagger_documentation">
        {t("public.dev.menu.swagger_documentation")}
      </Subitem>

      <Subitem href="#auth_api">
        {t("public.dev.menu.auth_api")}
      </Subitem>

      <Subitem href="#mt_api">
        {t("public.dev.menu.mt_api")}
      </Subitem>

      <Item href="#api_call">
        {t("public.dev.menu.api_call")}
      </Item>

      <Subitem href="#auth_sch">
        {t("public.dev.menu.auth_sch")}
      </Subitem>

      <Subitem href="#jwt_token">
        {t("public.dev.menu.jwt_token")}
      </Subitem>

      <Subitem href="#principle">
        {t("public.dev.menu.principle")}
      </Subitem>

      <Subitem href="#making_api">
        {t("public.dev.menu.making_api")}
      </Subitem>

      <Item href="#api_responce_handling">
        {t("public.dev.menu.api_responce_handling")}
      </Item>
      <Subitem href="#api_resp_code">
        {t("public.dev.menu.api_resp_code")}
      </Subitem>
      <Subitem href="#error_mess">
        {t("public.dev.menu.error_mess")}
      </Subitem>

      <Item href="#open_banking">
        {t("public.dev.menu.open_banking")}
      </Item>

      <Subitem href="#wh_psd2">
        {t("public.dev.menu.wh_psd2")}
      </Subitem>

      <Subitem href="#how_to_activ">
        {t("public.dev.menu.how_to_activ")}
      </Subitem>

      <Subitem href="#function_list">
        {t("public.dev.menu.function_list")}
      </Subitem>

      <Subitem href="#aisp">
        {t("public.dev.menu.aisp")}
      </Subitem>

      <Subitem href="#pisp">
        {t("public.Dev.menu.pisp")}
      </Subitem>
    </Navigation>
  );
}
