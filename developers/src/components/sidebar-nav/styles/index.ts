import styled from "styled-components";

export const Navigation = styled.nav`
  flex-direction: column;
  margin-right: 105px;
  min-width: 170px;
  position: sticky;
  display: flex;
  height: 100%;
  top: 30px;

  @media (max-width: 1200px) {
    margin-right: 6vw;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

export const Item = styled.a`
  color: ${({ theme }) => theme.colors.main};
  text-transform: uppercase;
  text-decoration: none;
  margin: 16px 0 10px;
  line-height: 26px;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;

  :hover {
    color:${({ theme }) => theme.colors.main_2};
  }

  :first-of-type {
    color:  ${({ theme }) => theme.colors.main_2};
    margin: 0;

    :hover {
      filter: brightness(110%);
      color: ${({ theme }) => theme.colors.main_2};
    }
  }
`;

export const Subitem = styled.a`
  color: ${({ theme }) => theme.colors.aux};
  text-decoration: none;
  text-transform: none;
  line-height: 26px;
  font-weight: 400;
  cursor: pointer;
  font-size: 14px;

  :hover {
    color: ${({ theme }) => theme.colors.main_2};
  }

  :first-of-type {
    margin-top: 30px;
  }

  :last-of-type {
    margin-bottom: 30px;
  }
`;
