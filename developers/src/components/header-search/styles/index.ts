import styled from "styled-components";
import { FaSearch } from "react-icons/fa";

export const Container = styled.form`
  box-sizing: content-box;
  border: 2px solid #fff;
  border-radius: 100px;
  position: relative;
  margin-right: 2rem;
  width: 250px;
`;

export const SearchInput = styled.input`
  font-family: "Roboto", sans-serif;
  background-color: transparent;
  padding-bottom: 0.7rem;
  padding-right: 2.5rem;
  padding-left: 0.5rem;
  padding-top: 0.7rem;
  font-size: 16px;
  outline: none;
  border: none;
  width: 100%;
  color: #fff;
`;

//TODO second active border color

export const ButtonWrapper = styled.button`
  background-color: transparent;
  transform: translateY(-50%);
  right: calc(0% + 0.4rem);
  position: absolute;
  border: none;
  color: #fff;
  top: 50%;
`;

export const SearchIcon = styled(FaSearch)`
  vertical-align: middle;
  font-size: 1.2rem;
`;
