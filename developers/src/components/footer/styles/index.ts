import styled from "styled-components";

export const Inner = styled.footer`
  background-color: #0d0d0d;
  justify-content: center;
  color: #fff;
  display: flex;
  z-index: 500;
  width: 100%;
`;

export const Container = styled.div`
  flex-direction: column;
  padding: 0 0 1rem;
  max-width: 1140px;
  display: flex;
  width: 100%;

  @media (max-width: 1200px) {
    padding: 0 1rem 0.5rem;
  }
`;

export const Navigation = styled.nav`
  align-items: center;
  font-weight: bold;
  list-style: none;
  flex-wrap: wrap;
  font-size: 14px;
  display: flex;
  padding: 0;
`;

export const List = styled.ul`
  align-items: center;
  font-weight: bold;
  list-style: none;
  margin: 0.8rem 0;
  flex-wrap: wrap;
  font-size: 14px;
  display: flex;
  padding: 0;
`;

export const Item = styled.li`
  @media (max-width: 600px) {
    justify-content: center;
    margin: 0 0 1rem;
    display: flex;
    width: 50%;
  }
`;

export const Title = styled.h3`
  text-align: center;
  margin: 0;
  margin-right: 1rem;
  font-size: 14px;
  color: #d3d8df;

  @media (max-width: 600px) {
    margin: 0;
  }
`;

export const Link = styled.a`
  text-decoration: none;
  margin-right: 1rem;
  color: #d3d8df;

  :hover {
    text-decoration: none;
  }

  @media (max-width: 600px) {
    margin: 0;
  }
`;

export const Description = styled.p`
  font-size: 12px;
  color: #d3d8df;
  margin: 0;

  @media (max-width: 600px) {
    text-align: center;
  }
`;
