import React from "react";

import { Inner, Container, Navigation, List, Item, Title, Description } from "./styles";
import { useTranslate } from "@common/hooks/useTranslate";

export default function Footer() {

  const { t } = useTranslate();

  function copyRight() {


    let text = t("shared.footer.copyright") as string;
    text = text.replace("#year#", new Date().getFullYear().toString());
    return text;
  }

  return (
    <Inner>
      <Container>
        <Navigation>
          <List>
            <Item>
              <Title>{copyRight()}</Title>
            </Item>
            {/*<Item>*/}
            {/*  <Link href="https://fundstr.com/docs/Fundstr-UAB_Terms-and-conditions.pdf" target="_blank">*/}
            {/*    { t("public.company.agreements.terms_and_conditions.title") }*/}
            {/*  </Link>*/}
            {/*</Item>*/}
            {/*<Item>*/}
            {/*  <Link href="https://fundstr.com/docs/Fundstr-UAB_Privacy-Policy.pdf" target="_blank">*/}
            {/*    { t("public.company.agreements.privacy_policy.title") }*/}
            {/*  </Link>*/}
            {/*</Item>*/}
            {/*<Item>*/}
            {/*  <Link*/}
            {/*    href="https://fundstr.com/docs/Fundstr-UAB_Procedure-for-settlement-of-disputes-and-complaints.pdf"*/}
            {/*    target="_blank"*/}
            {/*  >*/}
            {/*    { t("public.company.agreements.complaints_policy.title") }*/}
            {/*  </Link>*/}
            {/*</Item>*/}
          </List>
        </Navigation>
        <Description>
          {t("shared.footer.content")}
        </Description>
      </Container>
    </Inner>
  );
}
