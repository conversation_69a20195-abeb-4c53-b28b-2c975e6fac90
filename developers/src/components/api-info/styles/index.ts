import styled from 'styled-components';

export const Container = styled.section`
  box-sizing: border-box;
  width: 100%;
`;

export const Inner = styled.div`
  *[id] {
    //scroll-margin-top: 90px;
  }

  h1 {
    color: ${({ theme }) => theme.colors.main};
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 500;
    margin: 10px 0;

    :first-of-type {
      margin-top: 0;
    }
  }

  p {
    color: ${({ theme }) => theme.colors.aux};
    font-family: 'Open Sans', sans-serif;
    line-height: 2rem;
    margin: 0 0 24px;
    font-size: 1rem;
  }

  h2,
  h3 {
    color: ${({ theme }) => theme.colors.main};
    letter-spacing: 0.2px;
    line-height: 1.5rem;
    font-weight: 500;
    margin: 0 0 10px;
    font-size: 18px;
  }

  a {
    color: ${({ theme }) => theme.colors.aux};
    text-decoration: none;
  }

  ol {
    color: ${({ theme }) => theme.colors.aux};
    font-family: 'Open Sans', sans-serif;
    padding: 0;

    li {
      margin: 0.5rem 0 0.5rem 1.5rem;
      line-height: 24px;
    }
  }

  img {
    object-fit: cover;
    max-width: 100%;
  }

  pre {
    border: 1px solid ${({ theme }) => theme.colors.codeBorder};
    background: ${({ theme }) => theme.colors.ceil};
    white-space: pre-line;
    margin-bottom: 1.5rem;
    border-radius: 5px;
    padding: 10px;

    code {
      color: ${({ theme }) => theme.colors.codeColor};
      font-family: 'Open Sans', sans-serif;
      font-weight: 500;
      font-size: 12px;
    }
  }

  table {
    border-collapse: collapse;
    margin-bottom: 2rem;

    td {
      padding: 0.5rem 1rem;
    }

    tr {
      border-bottom: 1px solid ${({ theme }) => theme.colors.mainBorder};

      td {
        :first-child {
          p {
            border: 1px solid ${({ theme }) => theme.colors.mainBorder};
            background-color: ${({ theme }) => theme.colors.ceil};
            border-radius: 5px;
            text-align: center;
          }
        }
      }

      :first-of-type {
        border-bottom-width: 2px;

        td {
          p {
            color: ${({ theme }) => theme.colors.main};
            font-family: 'Roboto', sans-serif;
            line-height: 14px;
            font-weight: 700;
            font-size: 12px;
            margin: 0;
          }
        }
      }
    }

    p {
      color: ${({ theme }) => theme.colors.main};
      font-family: 'Open Sans', sans-serif;
      line-height: 14px;
      font-weight: 400;
      font-size: 12px;
      padding: 0.5rem;
      margin: 0;
    }
  }

  li {
    strong {
      color: ${({ theme }) => theme.colors.li};
    }
  }

  @media (max-width: 450px) {
    padding: 0;

    pre {
      padding: 10px 5px;
    }

    table {
      tr {
        td {
          vertical-align: top;
          padding: 10px 0;

          p {
            white-space: pre-line;
            line-height: 20px;
            text-align: center;
            padding: 0.3rem;
          }

          :first-child {
            p {
              border: 1px solid ${({ theme }) => theme.colors.mainBorder};
              background-color: ${({ theme }) => theme.colors.ceil};
              border-radius: 5px;
              text-align: center;
            }
          }
        }
      }
    }
  }
`;
