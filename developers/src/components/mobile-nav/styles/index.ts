import { motion } from "framer-motion";
import styled from "styled-components";

export const Selector = styled.button`
  border: 2px solid transparent;
  background-color: #555b61;
  box-sizing: border-box;
  border-radius: 5px;
  padding-left: 16px;
  position: sticky;
  cursor: pointer;
  margin: 2rem 0;
  display: none;
  outline: none;
  width: 200px;
  top: 30px;

  :focus {
    border: 2px solid #b6babd;
  }

  @media (max-width: 768px) {
    display: block;
  }
`;

export const SelectedValue = styled.span`
  position: relative;
  line-height: 16px;
  text-align: left;
  font-weight: 500;
  padding: 13px 0;
  font-size: 14px;
  display: block;
  color: white;

  ::after {
    border-right: 0.3rem solid transparent;
    border-left: 0.3rem solid transparent;
    border-top: 0.3rem solid white;
    transform: translateY(-50%);
    position: absolute;
    content: "";
    right: 10px;
    top: 50%;
  }
`;

export const Dropdown = styled(motion.div)`
  border: 1px solid #dee2e6;
  background-color: white;
  flex-direction: column;
  top: calc(100% + 5px);
  padding: 0.5rem 1rem;
  border-radius: 5px;
  position: absolute;
  overflow: hidden;
  display: flex;
  width: 100%;
  left: 0;
`;

export const Item = styled(motion.a)`
  text-decoration: none;
  line-height: 16px;
  padding: 0.5rem 0;
  text-align: left;
  font-size: 14px;
  color: #0d0d0d;
  width: 100%;
`;
