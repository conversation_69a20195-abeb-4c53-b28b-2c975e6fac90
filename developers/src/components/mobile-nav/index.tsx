import { AnimatePresence } from "framer-motion";
import React, { useState, useEffect, MouseEvent } from "react";
import { MobileLinks } from "./fixtures";

import { Selector, SelectedValue, Dropdown, Item } from "./styles";

export default function MobileNav() {
  const [isVisible, setIsVisible] = useState(false);

  function selectorToggler() {
    setIsVisible(false);
  }

  useEffect(() => {
    window.addEventListener("click", selectorToggler);

    return () => {
      window.removeEventListener("click", selectorToggler);
    };
  }, []);

  return (
    <Selector
      onClick={(e: MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        setIsVisible((prev) => !prev);
      }}
    >
      <SelectedValue>What is API</SelectedValue>
      <AnimatePresence>
        {isVisible && (
          <Dropdown
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.5 }}
          >
            {MobileLinks.map(({ href, title }) => (
              <Item key={href} href={href}>
                {title}
              </Item>
            ))}
          </Dropdown>
        )}
      </AnimatePresence>
    </Selector>
  );
}
