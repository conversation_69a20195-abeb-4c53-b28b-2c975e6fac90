# Install dependencies only when needed
#docker build -f developers/Dockerfile -t ryval-demo-developers .
#docker run -p 3000:3000 -t ryval-demo-developers
#docker run -p 3000:3000 -t ryval-demo-developers --add-host=host.docker.internal:host-gateway

FROM mirror.gcr.io/node:alpine AS builder

RUN npm install -g corepack
RUN corepack enable 
RUN corepack prepare yarn@4.3.1 --activate
RUN ls && yarn -v
RUN mkdir -p /tmp
COPY . ./tmp
WORKDIR /tmp

RUN yarn install --immutable
RUN export NODE_OPTIONS=--openssl-legacy-provider && yarn developers-build

FROM mirror.gcr.io/node:alpine AS runner

RUN corepack enable
RUN corepack prepare yarn@4.3.1 --activate
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0

WORKDIR /app

#ENV FileStorage home/nextjs/dev
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_TLS_REJECT_UNAUTHORIZED 0

# ENV apiBaseUrlInternal https://*************:5001
# ENV apiBaseUrl https://*************:5001
# ENV apiBaseUrlInternal https://apifront.test.koenigfinance.com
# ENV apiBaseUrl https://apifront.test.koenigfinance.com

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /tmp .

COPY --from=builder /tmp/developers/dist ./dist
COPY --from=builder /tmp/developers/src/public ./public
COPY --from=builder --chown=nextjs:nodejs /tmp/developers/.next ./.next

USER nextjs

EXPOSE 3000

CMD ["yarn", "developers-prod"]