import React from 'react';
import { pageService } from '@common/services/page-service';
import { supportReduxWrapper } from '@support/redux';
import NotFoundSupportPage from '@support/page/404';

export default function NotFoundPage() {
  return <NotFoundSupportPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(supportReduxWrapper, {
  propsLoader: NotFoundSupportPage.serverProps,
});
