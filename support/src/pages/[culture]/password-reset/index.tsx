import { pageService } from '@common/services/page-service';
import { supportReduxWrapper } from '@support/redux';
import PasswordResetSupportPage from '@support/page/locales-page/password-reset';

export default function () {
  return <PasswordResetSupportPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(supportReduxWrapper, {
  propsLoader: PasswordResetSupportPage.serverProps,
});
