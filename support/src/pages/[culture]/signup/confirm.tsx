import { pageService } from '@common/services/page-service';
import ConfirmPage from '@support/page/locales-page/signup/confirm';
import { supportReduxWrapper } from '@support/redux';

export default function (props) {
  return <ConfirmPage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(supportReduxWrapper, {
  propsLoader: ConfirmPage.serverProps,
});
