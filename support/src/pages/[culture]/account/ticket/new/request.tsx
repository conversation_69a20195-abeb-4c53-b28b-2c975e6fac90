import React from 'react';
import { pageService } from '@common/services/page-service';
import { supportReduxWrapper } from '@support/redux';
import RequestNewTicketPage from '@support/page/locales-page/account/ticket/new/request';

export default function () {
  return <RequestNewTicketPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(supportReduxWrapper, {
  onlyAuthorized: true,
});
