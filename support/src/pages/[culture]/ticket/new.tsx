import { useTranslate } from '@common/hooks/useTranslate';
import { pageService } from '@common/services/page-service';
import { supportReduxWrapper } from '@support/redux';
import AccountPage from '@support/page/locales-page/ticket/new';

export default function () {
  const { t } = useTranslate();

  return <AccountPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(supportReduxWrapper);
