import React from 'react';
import { ThemeProvider } from 'styled-components';
import { GlobalStyles } from '../theme/global-styles';
import { supportTheme } from '../theme/index';
import { supportReduxWrapper } from '@support/redux';
import { ModalOverlay } from '@common/components/modal-overlay';
import { SessionTimer } from '@common/components/session-timer';
import { TranslatesUpdate } from '@common/components/translates-update';
import { AppProps } from 'next/app';
import { ProgressLoader } from '@common/components/loaders/progress-loader';
import '@fortawesome/fontawesome-free/css/all.css';
import '@fancyapps/ui/dist/fancybox.css';

export default supportReduxWrapper.withRedux(function ({ Component, pageProps }: AppProps) {
  return (
    <>
      <TranslatesUpdate />
      {
        //@ts-ignore
        <ThemeProvider theme={supportTheme}>
          {
            //@ts-ignore
            <GlobalStyles />
          }
          <ProgressLoader />
          <Component {...pageProps} />
          <ModalOverlay />
          <SessionTimer />
        </ThemeProvider>
      }
    </>
  );
});
