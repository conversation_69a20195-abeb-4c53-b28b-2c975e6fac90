{"openapi": "3.0.1", "info": {"title": "Shared API", "description": "Build date 01.12.2022 10:36", "version": "v1"}, "paths": {"/v1/content": {"get": {"tags": ["Shared"], "parameters": [{"name": "language", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/languages": {"get": {"tags": ["Shared"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocationLanguageModelResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LocationLanguageModelResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocationLanguageModelResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/current-country": {"get": {"tags": ["Shared"], "parameters": [{"name": "headers", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/settings": {"get": {"tags": ["Shared"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SettingsModelResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SettingsModelResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SettingsModelResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/test": {"get": {"tags": ["Shared"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectListResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectListResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectListResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/goaway": {"get": {"tags": ["Shared"], "parameters": [{"name": "link", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/qr": {"get": {"tags": ["Shared"], "parameters": [{"name": "address", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/currency-converter": {"get": {"tags": ["Shared"], "parameters": [{"name": "amountFrom", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "currencyFrom", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "currencyTo", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ConverterResponseModelResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConverterResponseModelResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConverterResponseModelResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/fees/data/extended": {"get": {"tags": ["Shared"], "parameters": [{"name": "currencyID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "isPersonal", "in": "query", "schema": {"type": "boolean"}}, {"name": "businessCategoryID", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeesResponseExtendedModelResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeesResponseExtendedModelResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeesResponseExtendedModelResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/fees/data": {"get": {"tags": ["Shared"], "parameters": [{"name": "currencyID", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "isPersonal", "in": "query", "schema": {"type": "boolean"}}, {"name": "businessCategoryID", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeesResponseModelResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeesResponseModelResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeesResponseModelResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}, "/v1/enum-values": {"post": {"tags": ["Shared"], "parameters": [{"name": "SwaggerRequestHeader", "in": "header", "description": "For swagger request detection. Don't set anything here", "allowEmptyValue": true, "style": "simple", "example": "true"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnumValueRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumValueRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EnumValueRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringEnumValueResponeModelListDictionaryResponseFrontApi"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringEnumValueResponeModelListDictionaryResponseFrontApi"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringEnumValueResponeModelListDictionaryResponseFrontApi"}}}}, "400": {"description": "BadRequest"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "404": {"description": "NotFound"}, "403": {"description": "Forbidden"}, "503": {"description": "ServiceUnavailable"}}}}}, "components": {"schemas": {"ErrorModel": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "text": {"type": "string", "nullable": true}, "textTranslated": {"type": "string", "nullable": true}, "stack": {"type": "string", "nullable": true}, "errordata": {"type": "object", "additionalProperties": {"type": "object"}, "nullable": true}, "backendErrorData": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "backendErrorCode": {"type": "string", "nullable": true}}}, "StringStringDictionaryResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "Country": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "languages": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "WorldPart": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "countries": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}, "nullable": true}}}, "LanguageName": {"type": "object", "properties": {"language": {"items": {"$ref": "#/components/schemas/AAA"}}, "name": {"type": "string", "nullable": true}}}, "LocationLanguageModel": {"type": "object", "properties": {"worldParts": {"type": "array", "items": {"$ref": "#/components/schemas/WorldPart"}, "nullable": true}, "languagesAvailable": {"type": "array", "items": {"enum": ["ru = 1", "en = 2", "zh = 4", "ms = 8", "de = 16", "ar = 32", "fa = 64", "pt = 128", "es = 256", "fr = 512", "he = 1024", "ja = 2048", "el = 4096", "cs = 8192", "pl = 16384", "sk = 32768", "sv = 65536", "vi = 262144", "th = 524288", "et = 1048576", "hi = 2097152", "uk = 4194304", "it = 8388608", "tr = 16777216", "ko = 33554432", "id = 67108864", "ro = 134217728", "bn = 268435456", "hu = 536870912", "hy = 1073741824", "kk = 2147483648"], "type": "enum", "format": "int64"}, "nullable": true}, "languagesToShow": {"type": "array", "items": {"enum": ["ru = 1", "en = 2", "zh = 4", "ms = 8", "de = 16", "ar = 32", "fa = 64", "pt = 128", "es = 256", "fr = 512", "he = 1024", "ja = 2048", "el = 4096", "cs = 8192", "pl = 16384", "sk = 32768", "sv = 65536", "vi = 262144", "th = 524288", "et = 1048576", "hi = 2097152", "uk = 4194304", "it = 8388608", "tr = 16777216", "ko = 33554432", "id = 67108864", "ro = 134217728", "bn = 268435456", "hu = 536870912", "hy = 1073741824", "kk = 2147483648"], "type": "enum", "format": "int64"}, "nullable": true}, "allLanguages": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "allLanguagesCodes": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}, "nullable": true}, "allCountries": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}, "nullable": true}, "countriesToShow": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}, "nullable": true}, "languageNames": {"type": "array", "items": {"$ref": "#/components/schemas/LanguageName"}, "nullable": true}}}, "AAA": {"enum": ["ru = 1", "en = 2", "zh = 4", "ms = 8", "de = 16", "ar = 32", "fa = 64", "pt = 128", "es = 256", "fr = 512", "he = 1024", "ja = 2048", "el = 4096", "cs = 8192", "pl = 16384", "sk = 32768", "sv = 65536", "vi = 262144", "th = 524288", "et = 1048576", "hi = 2097152", "uk = 4194304", "it = 8388608", "tr = 16777216", "ko = 33554432", "id = 67108864", "ro = 134217728", "bn = 268435456", "hu = 536870912", "hy = 1073741824", "kk = 2147483648"], "type": "enum", "format": "int64"}, "LocationLanguageModelResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/LocationLanguageModel"}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "StringResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "CurrencyModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "symbol": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "chr": {"type": "string", "nullable": true}}}, "FileUploadRequirements": {"type": "object", "properties": {"filesCount": {"type": "integer", "format": "int32"}, "maxFileSize": {"type": "integer", "format": "int32"}, "allowedFileTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "SettingsModel": {"type": "object", "properties": {"systemBaseCurrency": {"$ref": "#/components/schemas/CurrencyModel"}, "captchaPublicKey": {"type": "string", "nullable": true}, "secureDomain": {"type": "string", "nullable": true}, "supportDomain": {"type": "string", "nullable": true}, "globalDomain": {"type": "string", "nullable": true}, "mainDomain": {"type": "string", "nullable": true}, "devDomain": {"type": "string", "nullable": true}, "timeoutMinutes": {"type": "integer", "format": "int32"}, "supportPasswordRequirements": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "accountPasswordRequirements": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "supportUploadFileRequirements": {"$ref": "#/components/schemas/FileUploadRequirements"}, "serverUtcOffset": {"type": "integer", "format": "int32"}}}, "SettingsModelResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/SettingsModel"}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "ObjectListResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"type": "object"}, "nullable": true}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "ConverterResponseModel": {"type": "object", "properties": {"amountTo": {"type": "string", "nullable": true}, "rate1": {"type": "string", "nullable": true}, "rate2": {"type": "string", "nullable": true}}}, "ConverterResponseModelResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ConverterResponseModel"}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "Fee_Currencies": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "desc": {"type": "string", "nullable": true}, "isBankSupport": {"type": "boolean"}}}, "Fee_WireFees_Type": {"type": "object", "properties": {"tariff_ID": {"type": "integer", "format": "int64"}, "bank_Tariff_ID": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}}}, "Fee_AdditionalFee": {"type": "object", "properties": {"amountTo": {"type": "number", "format": "double"}, "amountToText": {"type": "string", "nullable": true, "readOnly": true}, "amountFrom": {"type": "number", "format": "double"}, "amountFromText": {"type": "string", "nullable": true, "readOnly": true}, "currency": {"type": "string", "nullable": true}, "sha": {"type": "string", "nullable": true}, "ben": {"type": "string", "nullable": true}, "our": {"type": "string", "nullable": true}, "sepa": {"type": "string", "nullable": true}}}, "Fee_OtherCharge": {"type": "object", "properties": {"fix": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}}}, "ConverterRequestModel": {"type": "object", "properties": {"amountFrom": {"type": "string", "nullable": true}, "currencyFrom": {"type": "integer", "format": "int64"}, "currencyTo": {"type": "integer", "format": "int64"}}}, "FeesResponseExtendedModel": {"type": "object", "properties": {"currencies": {"type": "array", "items": {"$ref": "#/components/schemas/Fee_Currencies"}, "nullable": true}, "wireFees_Type": {"type": "array", "items": {"$ref": "#/components/schemas/Fee_WireFees_Type"}, "nullable": true}, "defaultCurrency": {"type": "integer", "format": "int64"}, "rubSystems": {"type": "array", "items": {"type": "string"}, "nullable": true}, "additionalFees": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Fee_AdditionalFee"}}, "nullable": true}, "paySystemFee_Deposit": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}, "paySystemFee_Withdrawal": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}, "wireServices": {"type": "array", "items": {"type": "string"}, "nullable": true}, "otherCharges": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Fee_OtherCharge"}, "nullable": true}, "converterFrom": {"$ref": "#/components/schemas/ConverterRequestModel"}, "converterTo": {"$ref": "#/components/schemas/ConverterResponseModel"}, "isPersonal": {"type": "boolean"}, "isUserLogin": {"type": "boolean"}, "clientShortName": {"type": "string", "nullable": true}, "isSepa": {"type": "boolean"}, "currencyFromID": {"type": "integer", "format": "int64"}, "businessCategoryID": {"type": "integer", "format": "int32", "nullable": true}}}, "FeesResponseExtendedModelResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/FeesResponseExtendedModel"}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "FeesResponseModel": {"type": "object", "properties": {"additionalFees": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Fee_AdditionalFee"}}, "nullable": true}, "paySystemFee_Deposit": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}, "paySystemFee_Withdrawal": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}, "wireServices": {"type": "array", "items": {"type": "string"}, "nullable": true}, "otherCharges": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Fee_OtherCharge"}, "nullable": true}, "converterFrom": {"$ref": "#/components/schemas/ConverterRequestModel"}, "converterTo": {"$ref": "#/components/schemas/ConverterResponseModel"}, "isPersonal": {"type": "boolean"}, "isUserLogin": {"type": "boolean"}, "clientShortName": {"type": "string", "nullable": true}, "isSepa": {"type": "boolean"}, "currencyFromID": {"type": "integer", "format": "int64"}, "businessCategoryID": {"type": "integer", "format": "int32", "nullable": true}}}, "FeesResponseModelResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/FeesResponseModel"}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}, "EnumValueRequestModel": {"type": "object", "properties": {"enums": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "EnumValueResponeModel": {"type": "object", "properties": {"value": {"type": "integer", "format": "int64"}, "translationKey": {"type": "string", "nullable": true}, "caption": {"type": "string", "nullable": true}}}, "StringEnumValueResponeModelListDictionaryResponseFrontApi": {"type": "object", "properties": {"successTextTranslated": {"type": "string", "nullable": true}, "data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/EnumValueResponeModel"}}, "nullable": true}, "result": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}, "nullable": true}, "isMaintenance": {"type": "boolean"}}}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Copy 'Bearer ' + valid JWT token into field", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}