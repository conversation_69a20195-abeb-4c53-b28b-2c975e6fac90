import React from 'react';
import { ThemeProvider } from 'styled-components';
import { GlobalStyles } from '../theme/global-styles';
import { theme } from '../theme/index';
import { secureReduxWrapper } from '@secure/redux';
import { ModalOverlay } from '@common/components/modal-overlay';
import { SessionTimer } from '@common/components/session-timer';
import { TranslatesUpdate } from '@common/components/translates-update';
import { AppProps } from 'next/app';
import { ProgressLoader } from '@common/components/loaders/progress-loader';
import { ToastProvider } from 'react-toast-notifications';
import useDataLoader from '@secure/hooks/useDataLoader';
import { PageTitle } from '@common/components/page-title';

import '@fortawesome/fontawesome-free/css/all.css';
import '@fancyapps/ui/dist/fancybox.css';

export default secureReduxWrapper.withRedux(function ({ Component, pageProps }: AppProps) {
  useDataLoader();
  return (
    <React.Fragment>
      <TranslatesUpdate />
      {
        //@ts-ignore
        <ThemeProvider theme={theme}>
          {
            //@ts-ignore
            <GlobalStyles />
          }
          <ProgressLoader />
          <ToastProvider
            newestOnTop
            autoDismiss={true}>
            <PageTitle {...(pageProps as any)}></PageTitle>
            <Component {...pageProps} />
            <ModalOverlay />
          </ToastProvider>
          <SessionTimer />
        </ThemeProvider>
      }
    </React.Fragment>
  );
});
