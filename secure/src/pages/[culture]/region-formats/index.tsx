import { pageService } from '@common/services/page-service';
import React from 'react';
import { secureReduxWrapper } from '@secure/redux';
import RegionFormatSecurePage from '@secure/page/locales-page/region-formats';

export default function (props) {
  return <RegionFormatSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: RegionFormatSecurePage.serverProps,
});
