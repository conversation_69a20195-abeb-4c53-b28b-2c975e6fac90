import { pageService } from '@common/services/page-service';
import { secureReduxWrapper, useAppSelector } from '@secure/redux';
import AccountSecurePage from '@secure/page/locales-page/accounts/account/[id]';

export default function () {
  return <AccountSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: AccountSecurePage.serverProps,
});
