import { pageService } from '@common/services/page-service';
import AccountProductSecurePage from '@secure/page/locales-page/accounts/account/[id]/products';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <AccountProductSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, { propsLoader: AccountProductSecurePage.serverLoader });
