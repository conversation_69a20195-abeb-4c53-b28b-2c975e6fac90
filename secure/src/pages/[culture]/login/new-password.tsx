import { UserLoginApiFrontModel } from '@common/http/services/auth-http-service';
import { pageService } from '@common/services/page-service';
import NewPasswordSecurePage from '@secure/page/locales-page/login/new-password';
import { secureReduxWrapper } from '@secure/redux';

export default function (props: { data: UserLoginApiFrontModel }) {
  return <NewPasswordSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: NewPasswordSecurePage.serverProps,
});
