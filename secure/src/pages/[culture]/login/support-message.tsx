import React from 'react';
import { pageService } from '@common/services/page-service';
import SupportMessageSecureLoginPage from '@secure/page/locales-page/login/support-message';
import { secureReduxWrapper } from '@secure/redux';

export default function SupportMessagePage() {
  return <SupportMessageSecureLoginPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SupportMessageSecureLoginPage.ServiceProps,
});
