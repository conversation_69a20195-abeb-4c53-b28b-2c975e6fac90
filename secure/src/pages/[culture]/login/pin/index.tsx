import { UserLoginApiFrontModel } from '@common/http/services/auth-http-service';
import { pageService } from '@common/services/page-service';
import { secureReduxWrapper } from '@secure/redux';
import PinSecurePage from '@secure/page/locales-page/login/pin';

export default function (props: { data: UserLoginApiFrontModel }) {
  return <PinSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: PinSecurePage.serverProps,
});
