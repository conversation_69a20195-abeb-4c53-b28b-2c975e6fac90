import { pageService } from '@common/services/page-service';
import PinSMSTroubleshootingSecurePage from '@secure/page/locales-page/login/pin/sms-troubleshooting';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <PinSMSTroubleshootingSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: PinSMSTroubleshootingSecurePage.ServiceProps,
});
