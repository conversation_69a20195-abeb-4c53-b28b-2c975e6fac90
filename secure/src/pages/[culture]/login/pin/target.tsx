import { pageService } from '@common/services/page-service';
import PinTargetSecurePage from '@secure/page/locales-page/login/pin/target';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <PinTargetSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: PinTargetSecurePage.ServiceProps,
});
