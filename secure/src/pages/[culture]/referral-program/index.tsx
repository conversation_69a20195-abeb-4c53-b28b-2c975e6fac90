import { pageService } from '@common/services/page-service';
import { secureReduxWrapper } from '@secure/redux';
import ReferralProgramPage from '@secure/page/locales-page/referral-program';

// TODO Предусмотреть возможность скрытия страницы

export default function () {
    return <ReferralProgramPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
    onlyAuthorized: true,
    propsLoader: ReferralProgramPage.serverProps
});
