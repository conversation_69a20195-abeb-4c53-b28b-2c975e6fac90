import { pageService } from '@common/services/page-service';
import VerificationSecurePage from '@secure/page/locales-page/profile/verification';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <VerificationSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: VerificationSecurePage.serverLoader,
});
