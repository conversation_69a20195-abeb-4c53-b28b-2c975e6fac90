import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import ProfileSecurePage from '@secure/page/locales-page/profile';

export default function () {
  return <ProfileSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: ProfileSecurePage.serverLoader,
});
