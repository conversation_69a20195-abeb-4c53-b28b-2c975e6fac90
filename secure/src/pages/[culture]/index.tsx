import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import MainSecurePage from '@secure/page/locales-page';

export default function (props) {
  return <MainSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: MainSecurePage.serverProps,
});
