import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import NotificationSecurePage from '@secure/page/locales-page/notifications';

export default function (props) {
  return <NotificationSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: NotificationSecurePage.serverProps,
});
