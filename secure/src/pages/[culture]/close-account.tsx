import { pageService } from '@common/services/page-service';
import React from 'react';
import { secureReduxWrapper } from '@secure/redux';
import CloseAccountSecurePage from '@secure/page/locales-page/close-account';

export default function () {
  return <CloseAccountSecurePage />;
}
export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
});
