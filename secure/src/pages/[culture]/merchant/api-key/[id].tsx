import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import ApiKeySecurePage from '@secure/page/locales-page/merchant/api-key/[id]';

export default function (props) {
  return <ApiKeySecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: ApiKeySecurePage.serverProps,
});
