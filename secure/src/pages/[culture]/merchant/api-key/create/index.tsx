import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import CreateApiKeySecurePage from '@secure/page/locales-page/merchant/api-key/create';

export default function (props) {
  return <CreateApiKeySecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: CreateApiKeySecurePage.serverProps,
});
