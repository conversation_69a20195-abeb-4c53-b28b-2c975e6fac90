import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import ApiKeysSecurePage from '@secure/page/locales-page/merchant/api-keys';

export default function (props) {
  return <ApiKeysSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: ApiKeysSecurePage.serverProps,
});
