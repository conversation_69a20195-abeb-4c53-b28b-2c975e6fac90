import { pageService } from '@common/services/page-service';
import SignupSubActivationSecurePage from '@secure/page/locales-page/signup/subuser-activation';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SignupSubActivationSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SignupSubActivationSecurePage.serverProps,
});
