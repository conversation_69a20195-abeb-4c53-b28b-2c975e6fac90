import { pageService } from '@common/services/page-service';
import SignupConfirmSecurePage from '@secure/page/locales-page/signup/confirm';
import { secureReduxWrapper } from '@secure/redux';

export default function (props) {
  return <SignupConfirmSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SignupConfirmSecurePage.serverProps,
});
