import { pageService } from '@common/services/page-service';
import SignupCheckPinPage from '@secure/page/locales-page/signup/check-pin';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SignupCheckPinPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SignupCheckPinPage.serverProps,
});
