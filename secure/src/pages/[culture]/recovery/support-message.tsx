import { pageService } from '@common/services/page-service';
import SupportMessageSecurePage from '@secure/page/locales-page/recovery/support-message';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SupportMessageSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SupportMessageSecurePage.ServiceProps,
});
