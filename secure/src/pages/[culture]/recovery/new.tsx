import { pageService } from '@common/services/page-service';
import RecoveryNewSecurePage from '@secure/page/locales-page/recovery/new';
import { secureReduxWrapper } from '@secure/redux';

export default function (props) {
  return <RecoveryNewSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: RecoveryNewSecurePage.ServiceProps,
});
