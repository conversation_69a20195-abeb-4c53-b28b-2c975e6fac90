import { pageService } from '@common/services/page-service';
import SMSTroubleShootingSecurePage from '@secure/page/locales-page/recovery/pin/sms-troubleshooting';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SMSTroubleShootingSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SMSTroubleShootingSecurePage.ServiceProps,
});
