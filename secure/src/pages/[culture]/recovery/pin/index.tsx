import { pageService } from '@common/services/page-service';
import RecoveryPinSecurePage from '@secure/page/locales-page/recovery/pin';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <RecoveryPinSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: RecoveryPinSecurePage.ServiceProps,
});
