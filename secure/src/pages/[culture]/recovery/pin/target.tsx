import { pageService } from '@common/services/page-service';
import RecoveryPinTargetSecurePage from '@secure/page/locales-page/recovery/pin/target';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <RecoveryPinTargetSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: RecoveryPinTargetSecurePage.ServiceProps,
});
