import { pageService } from '@common/services/page-service';
import SecurityQuestionsSecurePage from '@secure/page/locales-page/recovery/security-questions';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SecurityQuestionsSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: SecurityQuestionsSecurePage.ServiceProps,
});
