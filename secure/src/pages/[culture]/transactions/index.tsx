import { secureReduxWrapper } from '@secure/redux';
import { pageService } from '@common/services/page-service';
import TransactionsSecurePage from '@secure/page/locales-page/transactions';

export default function (props) {
  return <TransactionsSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: TransactionsSecurePage.serverProps,
});
