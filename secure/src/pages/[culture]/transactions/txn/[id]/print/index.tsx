import { pageService } from '@common/services/page-service';
import { secureReduxWrapper } from '@secure/redux';
import TransactionPrintSecurePage from '@secure/page/locales-page/transactions/txn/[id]/print';

export default function (props) {
  return <TransactionPrintSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: TransactionPrintSecurePage.serverProps,
});
