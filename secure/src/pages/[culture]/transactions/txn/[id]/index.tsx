import { pageService } from '@common/services/page-service';
import { secureReduxWrapper } from '@secure/redux';
import TransactionSecurePage from '@secure/page/locales-page/transactions/txn/[id]';

export default function (props) {
  return <TransactionSecurePage {...props} />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: TransactionSecurePage.serverProps,
});
