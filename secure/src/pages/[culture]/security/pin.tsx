import { pageService } from '@common/services/page-service';
import SecurityPinPage from '@secure/page/locales-page/security/pin';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SecurityPinPage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: SecurityPinPage.serverProps,
});
