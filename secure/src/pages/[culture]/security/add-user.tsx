import { pageService } from '@common/services/page-service';
import AddUserSecurePage from '@secure/page/locales-page/security/add-user';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <AddUserSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: AddUserSecurePage.serverProps,
});
