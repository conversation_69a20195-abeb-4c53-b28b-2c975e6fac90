import { pageService } from '@common/services/page-service';
import SecuritySecurePage from '@secure/page/locales-page/security';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <SecuritySecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: SecuritySecurePage.serverProps,
});
