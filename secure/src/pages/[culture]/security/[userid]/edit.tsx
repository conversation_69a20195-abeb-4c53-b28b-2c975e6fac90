import { pageService } from '@common/services/page-service';
import EditUserSecurePage from '@secure/page/locales-page/security/[userid]/edit';
import { secureReduxWrapper } from '@secure/redux';

export default function () {
  return <EditUserSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: true,
  propsLoader: EditUserSecurePage.ServiceProps,
});
