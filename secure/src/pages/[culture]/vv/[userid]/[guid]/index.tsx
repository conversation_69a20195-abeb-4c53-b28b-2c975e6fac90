import { pageService } from '@common/services/page-service';
import React from 'react';
import { secureReduxWrapper } from '@secure/redux';
import VideoVerification from '@secure/page/locales-page/vv/video-verification';

export default function () {
  return <VideoVerification />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  onlyAuthorized: false,
  propsLoader: VideoVerification.ServerProps
});
