import React from 'react';
import { pageService } from '@common/services/page-service';
import { secureReduxWrapper } from '@secure/redux';
import NotFoundSecurePage from '@secure/page/404';

export default function NotFoundPage() {
  return <NotFoundSecurePage />;
}

export const getServerSideProps = pageService.getServerSidePropsWithPageInitData(secureReduxWrapper, {
  propsLoader: pageService.notFoundPage,
});
