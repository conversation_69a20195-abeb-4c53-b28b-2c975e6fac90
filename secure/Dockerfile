# Install dependencies only when needed
#docker build -f secure/Dockerfile -t orange-demo-secure .
#docker run -p 3000:3000 -t orange-demo-secure

FROM mirror.gcr.io/node:alpine AS builder

RUN npm install -g corepack
RUN corepack enable 
RUN corepack prepare yarn@4.3.1 --activate
RUN ls && yarn -v
RUN mkdir -p /tmp
COPY . ./tmp
WORKDIR /tmp

RUN yarn install --immutable

RUN export NODE_OPTIONS=--openssl-legacy-provider && yarn secure-build

FROM mirror.gcr.io/node:alpine AS runner

RUN corepack enable
RUN corepack prepare yarn@4.3.1 --activate
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0

WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_TLS_REJECT_UNAUTHORIZED 0

# ENV apiBaseUrlInternal https://192.168.0.102:5001
# ENV apiBaseUrl https://192.168.0.102:5001

# ENV apiBaseUrlInternal https://apifront.payments.koenigfinance.com/
# ENV apiBaseUrl https://apifront.payments.koenigfinance.com/

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# COPY --from=builder /tmp/next.config.js ./next.config.js
# COPY --from=builder /tmp/node_modules ./node_modules
# COPY --from=builder /tmp/package.json ./package.json
# COPY --from=builder /tmp/yarn.lock ./yarn.lock
# COPY --from=builder /tmp/.yarn ./.yarn

COPY --from=builder /tmp .

COPY --from=builder /tmp/secure/dist ./dist
COPY --from=builder /tmp/secure/src/public ./public
COPY --from=builder --chown=nextjs:nodejs /tmp/secure/.next ./.next

USER nextjs

EXPOSE 3000
CMD ["yarn", "secure-prod"]
