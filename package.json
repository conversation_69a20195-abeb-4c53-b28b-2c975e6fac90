{"name": "orange-demo-app", "version": "0.1.0", "private": true, "scripts": {"api-section": "--------------------------------------------------------------------------------------------------", "codegen": "node common-data/general/swagger/index.js", "SUPPORT_COMMNADS": "---------------------------------------------------------------------------------------------", "support-dev": "cross-env NODE_ENV=development ts-node support/server/index.ts -config next.config.js", "support-dev-ssl": "cross-env NODE_ENV=development ts-node support/server/index.ts --ssl -config next.config.js", "support-build": "tsc --project support/tsconfig.server.json && cross-env NODE_ENV=production next build support", "support-prod": "cross-env NODE_ENV=production node dist/support/server/index.js", "PUBLIC_COMMNADS": "----------------------------------------------------------------------------------------------", "public-dev": "cross-env NODE_ENV=development ts-node public/server/index.ts -config next.config.js", "public-dev-ssl": "cross-env NODE_ENV=development ts-node public/server/index.ts --ssl -config next.config.js", "public-build": "tsc --project public/tsconfig.server.json && cross-env NODE_ENV=production next build public", "public-prod": "cross-env NODE_ENV=production node dist/public/server/index.js", "SECURE_COMMNADS": "----------------------------------------------------------------------------------------------", "secure-dev": "cross-env NODE_ENV=development ts-node secure/server/index.ts -config next.config.js", "secure-dev-ssl": "cross-env NODE_ENV=development ts-node secure/server/index.ts --ssl -config next.config.js", "secure-build": "tsc --project secure/tsconfig.server.json && cross-env NODE_ENV=production next build secure", "secure-prod": "cross-env NODE_ENV=production node dist/secure/server/index.js", "DEV_COMMNADS": "----------------------------------------------------------------------------------------------", "developers-dev": "cross-env NODE_ENV=development ts-node developers/server/index.ts -config next.config.js", "developers-dev-ssl": "cross-env NODE_ENV=development ts-node developers/server/index.ts --ssl -config next.config.js", "developers-build": "tsc --project developers/tsconfig.server.json && cross-env NODE_ENV=production next build developers", "developers-prod": "cross-env NODE_ENV=production node dist/developers/server/index.js"}, "dependencies": {"@fancyapps/ui": "^4.0.31", "@fortawesome/fontawesome-free": "^6.1.1", "@hookform/resolvers": "^2.6.1", "@microsoft/signalr": "^7.0.5", "@opentok/client": "^2.27.2", "@reduxjs/toolkit": "^1.6.1", "@shopify/react-compose": "^2.1.2", "axios": "^0.21.1", "cheerio": "^1.0.0-rc.10", "connect-redis": "^6.0.0", "cookie": "^0.4.1", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-winston": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^6.3.0", "fs-extra": "^10.1.0", "isomorphic-fetch": "^3.0.0", "js-cookie": "^2.2.1", "lodash": "^4.11.1", "lru-cache": "^6.0.0", "moment": "^2.29.3", "nanoid": "^3.1.25", "next": "^12.1.6", "next-redux-wrapper": "^7.0.2", "nextjs-progressbar": "^0.0.14", "normalize.css": "^8.0.1", "opentok-react": "^0.11.0", "qrcode.react": "^3.1.0", "rc-slider": "^10.2.1", "react": "17.0.2", "react-calendar": "^3.7.0", "react-color": "^2.19.3", "react-cropper": "^2.1.8", "react-dom": "17.0.2", "react-dropzone": "^11.3.4", "react-error-boundary": "^3.1.3", "react-google-recaptcha": "^2.1.0", "react-hook-form": "^7.15.4", "react-icons": "^4.2.0", "react-imask": "^6.4.2", "react-international-phone": "^4.3.0", "react-paginate": "^7.1.5", "react-papaparse": "^4.1.0", "react-phone-input-2": "^2.15.0", "react-redux": "^7.2.4", "react-router-config": "^5.1.1", "react-router-dom": "^5.2.0", "react-toast-notifications": "^2.5.1", "styled-components": "^5.3.5", "winston": "^3.3.3", "winston-aws-cloudwatch": "^3.0.0", "winston-daily-rotate-file": "^4.5.5", "worker-loader": "^3.0.8", "yup": "^0.32.9"}, "devDependencies": {"@types/cookie": "^0.4.1", "@types/cookie-parser": "^1.4.2", "@types/express": "^4.17.13", "@types/express-session": "^1.17.4", "@types/fancybox": "^3.5.3", "@types/js-cookie": "^3.0.0", "@types/lru-cache": "^5.1.1", "@types/next": "^9.0.0", "@types/node": "^16.6.1", "@types/nprogress": "^0.2.0", "@types/react": "^18.0.5", "@types/react-dom": "^18.0.1", "@types/react-paginate": "^7.1.1", "@types/react-redux": "^7.1.18", "@types/styled-components": "^5.1.25", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "cross-env": "^7.0.3", "eslint": "^8.13.0", "eslint-plugin-react": "^7.29.4", "swagger-typescript-api": "^9.3.1", "terser-webpack-plugin": "^5.3.3", "ts-node": "^10.7.0", "typescript": "^4.6.3"}, "resolutions": {"@types/react": "17.0.2"}, "packageManager": "yarn@4.8.1+sha512.bc946f2a022d7a1a38adfc15b36a66a3807a67629789496c3714dd1703d2e6c6b1c69ff9ec3b43141ac7a1dd853b7685638eb0074300386a59c18df351ef8ff6"}