variables:
  SUPPORT:
    value: 'NO'
    description: 'If the SUPPORT project needs to be built and deployed, write YES here. Any other word will cancel the building of the given project.'
  PUBLIC:
    value: 'NO'
    description: 'If the PUBLIC project needs to be built and deployed, write YES here. Any other word will cancel the building of the given project.'
  SECURE:
    value: 'NO'
    description: 'If the SECURE project needs to be built and deployed, write YES here. Any other word will cancel the building of the given project.'
  DEV:
    value: 'NO'
    description: 'If the DEV project needs to be built and deployed, write YES here. Any other word will cancel the building of the given project.'
  DEV_VERSION:
    value: '0.0.1'
    description: ''
    

stages:
  - version
  - build
  - test_build
  - test_deploy

.gitclone:
  tags:
    - amd64
  except:
    - web
  image: mirror.gcr.io/bitnami/git:2.33.0-debian-10-r60
  before_script:
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - ssh-keyscan "$CI_SERVER_HOST" > ~/.ssh/known_hosts && chmod 600 ~/.ssh/known_hosts
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    - git clone $(echo $CI_PROJECT_URL|sed 's/https:\/\//git@/'|sed 's/\//:/').git
    - cd "$CI_PROJECT_NAME"
    - git checkout master
  retry:
    max: 2
    when: runner_system_failure


version:
  extends: .gitclone
  only:
    refs:
      - master
    variables:
      - $CI_COMMIT_MESSAGE =~ /#major/i || $CI_COMMIT_MESSAGE =~ /#minor/i || $CI_COMMIT_MESSAGE =~ /#bugfix/i
  stage: version
  script:
    - export PREV_VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo 0.0.0)
    - echo $PREV_VERSION
    - >
      if   [[ "${CI_COMMIT_MESSAGE,,}" == *#major*  ]]; then export NEXT_VERSION=$(echo "$PREV_VERSION"|awk -F. '{$1++;print $1".0.0"}' OFS=.);
      elif [[ "${CI_COMMIT_MESSAGE,,}" == *#minor*  ]]; then export NEXT_VERSION=$(echo "$PREV_VERSION"|awk -F. '{$2++;print $1"."$2".0"}' OFS=.);
      elif [[ "${CI_COMMIT_MESSAGE,,}" == *#bugfix* ]]; then export NEXT_VERSION=$(echo "$PREV_VERSION"|awk -F. '{$3++;print}' OFS=.); fi
    - if [ -n "$NEXT_VERSION" ]; then git tag "$NEXT_VERSION"; git push origin "$NEXT_VERSION"; fi

build:
  tags:
    - amd64
  only:
    refs:
      - tags
      - master
    variables:
      - $CI_COMMIT_TAG
  except:
    - web
  stage: build
  retry:
    max: 2
    when: runner_system_failure
  image:
    name: gcr.io/kaniko-project/executor:v1.7.0-debug
    entrypoint: ['']
  variables:
    GIT_SUBMODULE_STRATEGY: normal
  script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/$DOCKERFILE
      --destination $CI_REGISTRY_IMAGE/$IMAGE_NAME:$CI_COMMIT_TAG
      --destination $CI_REGISTRY_IMAGE/$IMAGE_NAME:latest
  parallel:
    matrix:
      - IMAGE_NAME: support
        DOCKERFILE: support/Dockerfile
      - IMAGE_NAME: public
        DOCKERFILE: public/Dockerfile
      - IMAGE_NAME: dev
        DOCKERFILE: developers/Dockerfile
      - IMAGE_NAME: secure
        DOCKERFILE: secure/Dockerfile

.test-build:
  tags:
    - amd64
  only:
    refs:
      - web
  when: manual
  allow_failure: false
  stage: test_build
  image:
    name: gcr.io/kaniko-project/executor:v1.7.0-debug
    entrypoint: ['']
  variables:
    GIT_SUBMODULE_STRATEGY: normal
  before_script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
  retry:
    max: 2
    when: runner_system_failure

support_build:
  extends: .test-build
  only:
    variables:
      - $SUPPORT =~ /^YES$/i
  script:
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/support/Dockerfile
      --destination $CI_REGISTRY_IMAGE/support:test

public_build:
  extends: .test-build
  only:
    variables:
      - $PUBLIC =~ /^YES$/i
  script:
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/public/Dockerfile
      --destination $CI_REGISTRY_IMAGE/public:test

dev_build:
  extends: .test-build
  only:
    variables:
      - $DEV =~ /^YES$/i
  script:
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR/dev
      --dockerfile $CI_PROJECT_DIR/dev/Dockerfile
      --destination $CI_REGISTRY_IMAGE/dev:$DEV_VERSION

secure_build:
  extends: .test-build
  only:
    variables:
      - $SECURE =~ /^YES$/i
  script:
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/secure/Dockerfile
      --destination $CI_REGISTRY_IMAGE/secure:test

.test-deploy:
  tags:
    - amd64
  only:
    refs:
      - web
  stage: test_deploy
  image: mirror.gcr.io/bitnami/git:2.33.0-debian-10-r60
  before_script:
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - ssh-keyscan -p "$TEST_PORT" "$TEST_ADDRESS" > ~/.ssh/known_hosts && chmod 600 ~/.ssh/known_hosts
    - cat "$TEST_PEM" > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
  retry:
    max: 2
    when: runner_system_failure

support_deploy:
  extends: .test-deploy
  only:
    variables:
      - $SUPPORT =~ /^YES$/i
  script:
    - ssh "$TEST_USER@$TEST_ADDRESS" -p "$TEST_PORT"  "cd ~/devops/manifests/LocalProd && docker-compose pull supportreact && docker-compose up -d --force-recreate supportreact && docker system prune -f"

public_deploy:
  extends: .test-deploy
  only:
    variables:
      - $PUBLIC =~ /^YES$/i
  script:
    - ssh "$TEST_USER@$TEST_ADDRESS" -p "$TEST_PORT"  "cd ~/devops/manifests/LocalProd && docker-compose pull publicreact && docker-compose up -d --force-recreate publicreact && docker system prune -f"

secure_deploy:
  extends: .test-deploy
  only:
    variables:
      - $SECURE =~ /^YES$/i
  script:
    - ssh "$TEST_USER@$TEST_ADDRESS" -p "$TEST_PORT"  "cd ~/devops/manifests/LocalProd && docker-compose pull securereact && docker-compose up -d --force-recreate securereact && docker system prune -f"
