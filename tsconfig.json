{"ts-node": {"compilerOptions": {"module": "commonjs"}}, "compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@common/*": ["override-components/general/*", "common-data/general/*"], "@secure/*": ["override-components/secure/*", "common-data/secure/*"], "@public/*": ["override-components/public/*", "common-data/public/*"], "@support/*": ["override-components/support/*", "common-data/support/*"], "@dev/*": ["developers/src/*"]}}, "include": ["**/*.d.ts", "./next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}