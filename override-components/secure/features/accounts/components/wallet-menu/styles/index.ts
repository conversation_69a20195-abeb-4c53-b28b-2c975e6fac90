import styled from 'styled-components';

export const EndButton = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: unset;
  border: none;
  cursor: pointer;
  padding: 10px;
  margin: 0;
  min-width: 20px;
  min-height: 18px;
  color: ${({ theme }) => theme.colors.text.lightgray};
  transition: color 0.3s;
  font-size: 14px;
  font-weight: 700;
  &:hover {
    color: #000;
  }
`;

interface Menu {
  isOpen: boolean;
}
export const ButtonMenuContainer = styled.div<Menu>`
  position: absolute;
  background-color: ${({ theme }) => theme.colors.backgrounds.white};
  display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};
  top: 0;
  transform: translateY();
  z-index: 10;
  min-width: 100px;
  left: auto;
  right: 34px;
  padding: 5px 0 0;
  margin: 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
  &:after {
    content: '';
    margin-left: 0;
    margin-right: -10px;
    right: 0;
    border-left-color: #fff;
    position: absolute;
    z-index: 20;
    top: 10px;
    transform: translateY(25%);
    width: 0;
    height: 0;
    border-left: 5px solid #fff;
    border-right: 5px solid transparent;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
  }
`;
export const ButtonMenuButton = styled.div`
  padding: 4px 15px;
  white-space: nowrap;
  color: #727272;
  font-weight: 400;
  font-size: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  & > i {
    margin-right: 5px;
  }
  & > span {
    color: #262626;
  }
  &:hover {
    background-color: #f6f6f6;
  }
`;

export const ModalContainer = styled.div`
  width: 600px;
`;
export const ModalTitle = styled.h3`
  font-size: 24px;
  font-weight: ${({ theme }) => theme.fontWeight.bold};
`;
export const ModalTitleContainer = styled.div`
  padding: 15px 70px 15px 15px;
`;
export const ModalButtonContainer = styled.div`
  display: flex;
  padding: 15px;
  & > button {
    margin-right: 8px;
    &:last-child(1) {
      margin: 0;
    }
  }
`;
export const ModalDivider = styled.div`
  width: 100%;
  height: 2px;
  background-color: ${({ theme }) => theme.colors.gray};
`;
