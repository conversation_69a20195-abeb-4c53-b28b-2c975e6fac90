import useOutsideClick from '@common/hooks/useOutsideClick';
import { useTranslate } from '@common/hooks/useTranslate';
import { productsInfoList } from '@common/http';
import { AccountInformationModel } from '@common/http/services/secure-http-service';
import React, { useEffect, useState } from 'react';
import { useToasts } from 'react-toast-notifications';
import { useAppSelector } from '@secure/redux';
import { ButtonMenuButton, ButtonMenuContainer } from '@secure/features/accounts/components/wallet-menu/styles';
import { useWalletMenu } from '@secure/features/accounts/components/wallet-menu/internal-components';

interface WalletMenuProps {
  isOpen: boolean;
  setIsOpen: (e: boolean) => void;
  account: AccountInformationModel;
}

function WalletMenu({ isOpen, setIsOpen, account }: WalletMenuProps, ref: React.MutableRefObject<HTMLButtonElement>) {
  const { addToast } = useToasts();
  const { t } = useTranslate();
  const { accounts } = useAppSelector((state) => state.accountsInfo);
  const { isFinancesFullAccess } = useAppSelector((state) => state.userProfile);
  const { isOpenAccountAsProduct } = useAppSelector((state) => state.commonData);
  const { toggleEditWalletSetting, toggleBankDetail, toggleProductsLink, toggleRemoveModalOpen } =
    useWalletMenu(account);

  const [productsAvailable, setProductsAvailable] = useState(null);

  useOutsideClick(ref, onCloseMenu, isOpen);

  function onCloseMenu(e: MouseEvent) {
    setIsOpen(false);
  }

  const showDeleteButton =
    isFinancesFullAccess && accounts.length > 1 && account.availableBalance == 0 && account.holdBalance == 0;

  function checkProductsIsAvailable() {
    // TODO поставили заплатку, потом надо бы сделать отдельную переменную для того что бы показывать или нет эту кнопку
    if (isOpenAccountAsProduct) {
      return setProductsAvailable(false)
    }
    productsInfoList().then(function (res) {
      if (res.errors?.length) {
        addToast(<div dangerouslySetInnerHTML={{ __html: res.errors[0].textTranslated || res.errors[0].text }} />);
        return;
      }
      let availiableForOrder = res.data.availiableForOrder.length > 0;
      let orders = res.data.orders.length > 0;
      setProductsAvailable(availiableForOrder == true || orders == true);
    });
  }

  useEffect(() => {
    checkProductsIsAvailable();
  }, []);

  return (
    <>
      <ButtonMenuContainer isOpen={isOpen}>
        <ButtonMenuButton onClick={toggleEditWalletSetting}>
          <i className='fa-solid fa-gear'></i>
          <span>{t('secure.index.accounts_wallets_menu_Properties')}</span>
        </ButtonMenuButton>

        <ButtonMenuButton onClick={toggleBankDetail}>
          <i className='fa-solid fa-square-plus'></i>
          <span>{t('secure.index.accounts_wallets_menu_GetBankDetails')}</span>
        </ButtonMenuButton>
        {productsAvailable && (
          <ButtonMenuButton onClick={toggleProductsLink}>
            <i className={'fa fa-university'} />
            <span>{t('secure.index.products_link')}</span>
          </ButtonMenuButton>
        )}
        {showDeleteButton && (
          <ButtonMenuButton onClick={toggleRemoveModalOpen}>
            <i className='fa-solid fa-trash-can'></i>
            <span>{t('secure.accounts.index.Wallet_Menu_Delete')}</span>
          </ButtonMenuButton>
        )}
      </ButtonMenuContainer>
    </>
  );
}

export default React.forwardRef(WalletMenu);
