import styled, { css } from 'styled-components';

export const Outer = styled.section`
  background-color: ${({ theme }) => theme.colors.backgrounds.default};
  justify-content: center;
  display: flex;
  width: 100%;
`;

export const Container = styled.div`
  padding: 2.5rem 2rem;
  position: relative;
  max-width: 1300px;
  display: flex;
  width: 100%;

  @media (max-width: 1000px) {
    flex-direction: column;
  }

  @media (max-width: 750px) {
    padding: 2.5rem 1rem;
  }
`;

export const Header = styled.div`
  background: url('/stay-in-touch.png');
  transform: translateY(-25%);
  justify-content: center;
  position: absolute;
  height: 46px;
  width: 151px;
  left: 0;
  top: 0;
  /* display: flex; */
  display: none;
  span {
    font-style: italic;
    margin-top: 1rem;
    color: white;
  }
`;

interface IColumn {
  marginRight?: 'true';
  marginLeft?: 'true';
  maxWidth?: string;
}

export const Column = styled.div<IColumn>`
  ${({ marginLeft }) =>
    marginLeft &&
    css`
      margin-left: auto;

      @media (max-width: 1000px) {
        margin-left: 0;
      }
    `};

  ${({ marginRight }) =>
    marginRight &&
    css`
      margin-right: 4rem;
    `};

  ${({ maxWidth }) =>
    maxWidth &&
    css`
      max-width: ${maxWidth};

      @media (max-width: 1000px) {
        max-width: 100%;
        margin: 0 !important;
      }
    `};

  :last-of-type {
    margin-right: 4rem;
  }
`;

export const Title = styled.h4`
  font-size: calc(${({ theme }) => theme.fontSize.large} + 0.25rem);
  font-weight: ${({ theme }) => theme.fontWeight.light};
  color: ${({ theme }) => theme.colors.main};
  margin: 0.5rem 0;

  @media (max-width: 1000px) {
    &.border {
      padding: 1rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
`;

export const Link = styled.a`
  font-size: ${({ theme }) => theme.fontSize.default};
  color: #555;

  :hover {
    text-decoration: none;
  }
`;

export const Adress = styled.p`
  font-size: ${({ theme }) => theme.fontSize.default};
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-word;
  color: #555;
`;

export const Map = styled.p`
  color: #555;

  i {
    margin-right: 0.5rem;
  }
`;

export const Row = styled.ul`
  list-style: none;
  flex-wrap: wrap;
  display: flex;
  padding: 0;
  margin: 0;
`;

interface ItemProps {
  item: 'rss' | 'instagram' | 'vk' | 'forum' | 'twitter' | 'youtube' | 'facebook';
}

export const Item = styled.a<ItemProps>`
  box-shadow: 0 2px 2px 0 rgb(0 0 0 / 30%);
  background: url('/social-sprites.png');
  border-radius: 50%;
  transition: 300ms;
  margin: 0.125rem;
  display: block;
  height: 30px;
  width: 30px;

  ${({ item }) =>
    item === 'facebook' &&
    css`
      background-position: 0 -120px;

      :hover {
        background-position: 0 -150px;
      }
    `}

  ${({ item }) =>
    item === 'rss' &&
    css`
      background-position: 0 -900px;

      :hover {
        background-position: 0 -930px;
      }
    `}

  ${({ item }) =>
    item === 'instagram' &&
    css`
      background-position: 0 -1680px;

      :hover {
        background-position: 0 -1710px;
      }
    `}

  ${({ item }) =>
    item === 'vk' &&
    css`
      background-position: 0 -1860px;

      :hover {
        background-position: 0 -1890px;
      }
    `}

  ${({ item }) =>
    item === 'twitter' &&
    css`
      background-position: 0 -1140px;

      :hover {
        background-position: 0 -1170px;
      }
    `}

  ${({ item }) =>
    item === 'youtube' &&
    css`
      background-position: 0 -1380px;

      :hover {
        background-position: 0 -1410px;
      }
    `}

  ${({ item }) =>
    item === 'forum' &&
    css`
      background-position: 0 -1920px;

      :hover {
        background-position: 0 -1950px;
      }
    `}
`;
