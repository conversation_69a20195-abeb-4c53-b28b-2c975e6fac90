import Head from 'next/head';
import { MainLayout } from '@common/layout/main';
import { useTranslate } from '@common/hooks/useTranslate';
import HomePagePaymentInfoContainer from '@public/containers/home-page/payment-info';
import HomePageServicesInfoContainer from '@public/containers/home-page/services-info';
import HomePageSliderContainer from '@public/containers/home-page/slider';
import HomePageThumbnailContainer from '@public/containers/home-page/thumbnail';
import SocialTapeFeature from '@public/features/social-tape';
import CommingSoonComponent from '@public/components/comming-soon';

export default function MainPage() {
  const { t } = useTranslate();

  return (
    <>
      <Head>
        <title>{t('public.index.title')}</title>
      </Head>
      <MainLayout>
        <CommingSoonComponent />
        {/* <HomePageThumbnailContainer />
        <HomePagePaymentInfoContainer />
        <HomePageServicesInfoContainer />
        <HomePageSliderContainer /> */}
        <SocialTapeFeature />
      </MainLayout>
    </>
  );
}

MainPage.serverProps = () => ({
  props: {},
});
