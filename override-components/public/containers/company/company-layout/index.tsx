import React, { PropsWithChildren } from 'react';
import { useTranslate } from '@common/hooks/useTranslate';
import NavigationCompanyComponent from '@public/containers/company/company-navigation';

import { Inner, Container, Content, ContainerWithoutGrid } from '@public/containers/company/styles';

export default function CompanyLayoutWithNavigation(props: PropsWithChildren<{}>) {
  const { t } = useTranslate();
  return (
    <Inner>
      {/* <Container> */}
      <ContainerWithoutGrid>{props.children}</ContainerWithoutGrid>
      {/* <NavigationCompanyComponent /> */}
      {/* </Container> */}
    </Inner>
  );
}
