import styled from 'styled-components';

export const Inner = styled.footer`
  border-top: 2px solid ${({ theme }) => theme.colors.gray};
  justify-content: center;
  margin-top: auto;
  padding: 35px 0;
  display: flex;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.backgrounds.white};
  @media (max-width: 1300px) {
    padding: 1rem 2rem;
  }

  @media (max-width: 750px) {
    padding: 1rem;
  }
`;

export const Container = styled.div`
  font-size: ${({ theme }) => theme.fontSize.medium};
  justify-content: space-between;
  max-width: 1170px;
  display: flex;
  width: 100%;

  @media (max-width: 750px) {
    flex-direction: column;
    align-items: center;
  }
`;

export const Wrapper = styled.div`
  display: flex;
  flex: 1 1 0%;

  @media (max-width: 750px) {
    align-items: center;
  }
`;

export const LogoWrapper = styled.a`
  cursor: pointer;
`;

export const Logo = styled.img`
  height: 40px;
`;

export const Description = styled.p`
  color: ${({ theme }) => theme.colors.text.lightgray};
  margin: 0 2rem 0;

  @media (max-width: 750px) {
    margin: 0 1rem 0;
  }
`;

export const Nav = styled.nav`
  flex-wrap: wrap;
  display: flex;
  gap: 5px;
  flex: 1 1 0%;

  @media (max-width: 750px) {
    margin-top: 1rem;
    max-width: 100%;
  }
`;

export const Link = styled.a`
  color: ${({ theme }) => theme.colors.main};
  border-right: 1px solid rgba(0, 0, 0, 0.3);
  text-decoration: none;
  padding-right: 0.5rem;
  cursor: pointer;
  height: max-content;

  :last-of-type {
    border-right: none;
    padding: 0;
  }

  :hover {
    color: ${({ theme }) => theme.colors.aux};
    text-decoration: underline;
  }
`;
