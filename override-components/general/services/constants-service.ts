//On editing this file need to edit  public-sitemap.ts with menu items

import { Menu } from '@common/models/Menu';

class ConstantsService {
  mainMenu: Menu = {
    items: [
      // {
      //   url: '/personal',
      //   name: 'public.personal.index.title',
      //   domain: 'public',
      //   children: {
      //     top: [
      //       [
      //         {
      //           name: 'public.index.services_card_title',
      //           url: '/personal/payment-cards',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.index.services_wallet_title',
      //           url: '/personal/digital-wallet',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.index.services_promotions_title',
      //           url: '/personal/promotions',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.partners.referral_program.title',
      //           url: '/partners/referral-program',
      //           domain: 'public',
      //         },
      //       ],
      //     ],
      //     bottom: [
      //       {
      //         name: 'public.personal.fees.title',
      //         url: '/personal/fees',
      //         domain: 'public',
      //       },
      //     ],
      //   },
      // },
      // {
      //   url: '/business',
      //   name: 'enum.originoffunds.origin_business',
      //   domain: 'public',
      //   children: {
      //     top: [
      //       [
      //         {
      //           name: 'public.index.services_title',
      //           url: '/business',
      //           domain: 'public',
      //           title: true,
      //         },
      //         {
      //           name: 'public.index.services_payments_title',
      //           url: '/business/accepting-payments',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'enum.paysystemservices.payout_payroll_affiliate',
      //           url: '/business/payouts',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.index.services_wallet_title',
      //           url: '/business/digital-wallet',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.index.services_banking_title',
      //           url: '/business/multicurrency-account',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'enum.paysystemservices.other_client_verification',
      //           url: '/business/client-verification',
      //           domain: 'public',
      //         },
      //       ],
      //       [
      //         {
      //           name: 'public.layout.menu_solutions',
      //           url: '/business/solutions',
      //           domain: 'public',
      //           title: true,
      //         },
      //         {
      //           name: 'public.business.solutions.freelance.title',
      //           url: '/business/solutions/freelance',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.business.solutions.retail.title',
      //           url: '/business/solutions/retail',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.business.solutions.digital_content.title',
      //           url: '/business/solutions/digital-content',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.business.solutions.b2b.title',
      //           url: '/business/solutions/b2b',
      //           domain: 'public',
      //         },
      //         {
      //           name: 'public.business.solutions.financial_services.title',
      //           url: '/business/solutions/financial-services',
      //           domain: 'public',
      //         },
      //       ],
      //     ],
      //     bottom: [{ url: '/business/fees', name: 'public.business.fees.title', domain: 'public' }],
      //   },
      // },
      {
        url: '/',
        name: 'support.title',
        domain: 'support',
        children: {
          top: [
            [
              {
                name: 'public.layout.faq',
                url: '/faq',
                domain: 'support',
              },
              {
                name: 'support.manuals.index.title',
                url: '/manuals',
                domain: 'support',
              },
              {
                name: 'support.video.index.title',
                url: '/video',
                domain: 'support',
              },
              {
                name: 'support.communities.index.title',
                url: '/communities',
                domain: 'support',
              },
              {
                name: 'support.security.title',
                url: '/security',
                domain: 'support',
              },
            ],
          ],
          bottom: [
            {
              name: 'support.account.ticket_new.title',
              url: '/ticket/new',
              domain: 'support',
            },
            {
              name: 'support.inners.requests',
              url: '/account',
              domain: 'support',
            },
          ],
        },
      },
    ],
  };

  footerMenu: Menu = {
    items: [
      {
        name: 'public.index.title',
        url: '/',
        domain: 'public',
      },
      {
        name: 'public.sitemap.title',
        url: '/sitemap',
        domain: 'public',
      },
      {
        name: 'public.company.agreements.index.title',
        url: '/company/agreements',
        domain: 'public',
      },
      {
        name: 'public.company.agreements.privacy_policy.title',
        url: '/company/agreements/privacy-policy',
        domain: 'public',
      },
      {
        name: 'public.partners.index.title',
        url: '/partners',
        domain: 'public',
      },
      {
        name: 'public.company.index.title',
        url: '/company',
        domain: 'public',
      },
    ],
  };
}

export const constantsService = new ConstantsService();
