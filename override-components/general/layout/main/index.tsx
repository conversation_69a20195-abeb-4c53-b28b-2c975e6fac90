import React, { PropsWithChildren, useState } from 'react';
import { LayoutContent, LayoutWrapper } from '@common/layout/main/styles';
import { Header } from '@common/components/styled/header';
import { Footer } from '@common/components/styled/footer';
import { <PERSON><PERSON>oggler } from '@common/components/lang-toggler';
import { MainMenu } from '@common/components/navigations/main-menu';
import { constantsService } from '@common/services/constants-service';
import { useTranslate } from '@common/hooks/useTranslate';
import { AnimatePresence } from 'framer-motion';
import { MobileMenu } from '@common/components/navigations/mobile-menu';
import { AuthButtons } from '@common/components/buttons/auth-buttons';
import { useAppSelector } from '@common/hooks/useAppSelector';

export function MainLayout({ children }: PropsWithChildren<{}>) {
  const { mainDomain } = useAppSelector(state => state.settings);

  const footerMenu = constantsService.footerMenu;
  const [mobileMenuActive, setMobileMenuActive] = useState(false);
  const { t } = useTranslate();

  function copyRight(): string {
    return t('public.layout.footer_copyright').replace('#year#', new Date().getFullYear().toString());
  }

  function handleMobileMenu() {
    if (mobileMenuActive) document.body.style.overflowY = 'auto';
    else document.body.style.overflowY = 'hidden';
    setMobileMenuActive((prev) => !prev);
  }

  return (
    <LayoutWrapper>
      <Header>
        <Header.Inner>
          <Header.Container>
            <Header.LinkWrapper href={mainDomain}>
              <Header.Logo src='/logo.svg' />
            </Header.LinkWrapper>
            <Header.Aside>
              <Header.Row>
                <LangToggler />
              </Header.Row>
              <Header.Row>
                <MainMenu />
                <AuthButtons />
              </Header.Row>
              <Header.MobileButton onClick={handleMobileMenu} />
            </Header.Aside>
          </Header.Container>
        </Header.Inner>
        <AnimatePresence>{mobileMenuActive && <MobileMenu />}</AnimatePresence>
      </Header>
      <LayoutContent>{children}</LayoutContent>
      <Footer>
        <Footer.Container>
          <Footer.Copyright>
            <Footer.LogoWrapper href={mainDomain}>
              <Footer.Logo src='/img/footer-logo.svg' />
            </Footer.LogoWrapper>
            <Footer.Description>{copyRight()}</Footer.Description>
          </Footer.Copyright>
          {/* <Footer.Nav>
            {footerMenu.items.map((item) => (
              <Footer.Link key={item.url} href={`${mainDomain}${item.url}`}>
                {t(item.name)}
              </Footer.Link>
            ))}
          </Footer.Nav> */}
        </Footer.Container>
      </Footer>
    </LayoutWrapper>
  );
}
