export const mainTheme = {
  focus: {
    shadow: '0 0 5px #3d557e;',
    altShadow: '0 0 4px 1px #7ab9ec',
    border: '1px solid #3d557eb3;',
    button: '#ccc',
  },
  borders: {
    default: '1px solid #00000033',
  },
  fonts: {
    input: `"Open Sans", sans-serif`,
    main: `"Open Sans", sans-serif`,
  },
  fontSize: {
    verySmall: '10px',
    small: '12px',
    medium: '13px',
    default: '14px',
    big: '16px',
    settingDesc: '18px',
    large: '20px',
    settingTitle: '25px',
    huge: '36px',
    veryLarge: '30px',
    giant: '45px',
  },
  fontWeight: {
    verryfat: '900',
    ultraFat: '700',
    fat: '600',
    bold: '500',
    light: '300',
    regular: '400',
  },
  shadow: {
    dropdown: '0 6px 12px #0000002e',
    imgBorder: '0 0 0 2px #ccc',
    popover: '0px 2px 3px #ccc',
  },
  colors: {
    main: '#0c1e30',
    button: '#263858',
    link: '#0067da',
    beneficiariesActionButton: '#f2f2f2',
    beneficiariesActionButtonText: '#ааа',
    beneficiariesActionButtonHover: '#cecece',
    aux: '#55749a',
    red: '#d9534f',
    error: '#dc746d',
    focus: '#7ab9ec',
    gray: '#ddd',
    icon: '#ccc',
    shadow: '#3d557e',
    border: '#5e7297',
    avatarBorder: '#0c1e30',
    orangeLight: '#263858',
    gross: {
      in: "#83b254",  //default #83b254
      out: "#dc746d",
    },
    table: {
      background: '#f7f8f9',
      text: '#563737',
      header: {
        background: '#F2F3F4',
        text: '#9b9b9b',
        border: '#EBEBEB',
      },
      row: {
        background: '#f7f8f9',
        text: '#040404',
        border: '#EBEBEB',
      },
    },
    text: {
      default: '#333',
      gray: '#444',
      lightgray: '#999',
      darkgray: '#555',
      meduimgray: '#7c7c7c',
      blue: '#337ab7',
      white: '#fff',
      theme: '#ffe3a6',
      themeSaturated: '#234f7a',
      button: '#fff',
      buttonDark: '#000',
      beneficiariesLinkButton: '#000',
      // TODO add color beneficiariesActionButton
      beneficiariesActionButton: '#fff',
      contactNotVerifyChip: '#fff',
      contactNotVerifyChipHover: '#fff'
    },
    backgrounds: {
      default: '#f2f3f4',
      white: '#fff',
      gray: '#f7f7f7',
      disabled: '#ccc',
      popover: '#f4f0df',
      main: '#55749a',
      radioButton: '#eff5e6',
      contactNotVerifyChip: '#f9ac65',

    },
    inputs: {
      disabled: '#eee',
      background: '#fff',
      fontColor: '#555',
      border: '#ccc',
      shadow: 'inset 0 1px 1px #00000014',
      error: '#dc746d',
      radioButton: '#64a954',
      active: {
        shadow: 'inset 0 12px 19px #a8a8a6',
      },
      focus: {
        borderNormal: ' #66afe9',
        borderError: ' #d24d44',
        shadowNormal: 'inset 0 1px 1px #00000014, 0 0 8px #66afe999',
        shadowError: 'inset 0 1px 1px #00000014, 0 0 6px #f0c2bf',
      },
    },
    alert: {
      bg: '#e4ac17',
      border: '#e6b91e',
    },
    icons: {
      background: "#e6e6e6",
      main: "#0c1e30"
    }
  },
  hover: {
    navigationLink: '#2d3845',
    icon: '#5f5d5d',
    button: '#f5f5f5',
    buttonText: '#fff',
    link: '#2d3845',
    background: {
      link: '#f6ce954d',
      button: '#0c1e30',
      contactNotVerifyChipHover: '#fd9533'
    },
  },
};
